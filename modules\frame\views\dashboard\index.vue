<template>
    <div class="view">
        <div class="container">
            <h1>{{ title }}</h1>
        </div>
    </div>
</template>

<script>
import funcitons from '../../mixins/functions';

export default {
    name: 'DashboardIndex',
    mixins: [funcitons],
    data() {
        return {

        };
    },
    computed: {
        title() {
            return this.$t(this.$store.state.settings.title);
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
    },
    methods: {
    }
};
</script>

<style lang="scss" scoped>
.container{
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
