/* eslint-disable require-jsdoc */
/* eslint-disable max-lines-per-function */
/* eslint-disable no-shadow */
/* eslint-disable line-comment-position */
/* eslint-disable no-inline-comments */
'use strict';
const path = require('path');
const defaultSettings = require('./src/settings.js');

function resolve(dir) {
    return path.join(__dirname, dir);
}

const name = defaultSettings.title || '服务信息化平台'; // page title

// const moduleName = require('./package.json').moduleName;

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following method:
// port = 9527 npm run dev OR npm run dev --port = 9527
const port = process.env.port || process.env.npm_config_port || 80; // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
    /**
     * You will need to set publicPath if you plan to deploy your site under a sub path,
     * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
     * then publicPath should be set to "/bar/".
     * In most cases please use '/' !!!
     * Detail: https://cli.vuejs.org/config/#publicpath
     */
    // publicPath: '/',
    publicPath: '/',
    outputDir: 'bossManager',
    assetsDir: 'static',
    lintOnSave: process.env.NODE_ENV === 'development',
    productionSourceMap: false,
    devServer: {
        port,
        open: true,
        overlay: {
            warnings: false,
            errors: true
        },
        disableHostCheck: true,
        https: false
        // before: require('./mock/mock-server.js')
    },
    configureWebpack: {
        // provide the app's title in webpack's name field, so that
        // it can be accessed in index.html to inject the correct title.
        name,
        // externals: cdn.externals,
        resolve: {
            alias: {
                '@': resolve('src'),
                'frame': resolve('modules/frame'),
                'systemManagement': resolve('modules/systemManagement')
            }
        }
    },
    // configureWebpack: (config) => {
    //   config.name = name
    //   config.resolve = {
    //     alias: {
    //       '@': resolve('src')
    //     }
    //   }
    //   // config.entry = Object.assign({}, config.entry, newEntries);
    //   // config.entry = {
    //   //   index: 'src/index/main.js',
    //   //   modules: MODULES_ENTRY
    //   // }
    // },
    chainWebpack(config) {
        // it can improve the speed of the first screen, it is recommended to turn on preload
        // it can improve the speed of the first screen, it is recommended to turn on preload
        config.plugin('preload').tap(() => [
            {
                rel: 'preload',
                // to ignore runtime.js
                // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
                fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
                include: 'initial'
            }
        ]);

        // when there are many pages, it will cause too many meaningless requests
        config.plugins.delete('prefetch');

        // set svg-sprite-loader
        config.module
            .rule('svg')
            .exclude.add(resolve('modules'))
            .add(resolve('node_modules/wtf-core-vue-ng/src/icons'))
            .end();
        config.module
            .rule('icons')
            .test(/\.svg$/)
            .include.add(resolve('modules'))
            .add(resolve('node_modules/wtf-core-vue-ng/src/icons'))
            .end()
            .use('svg-sprite-loader')
            .loader('svg-sprite-loader')
            .options({
                symbolId: 'icon-[name]'
            })
            .end();

        config.when(process.env.NODE_ENV !== 'development', (config) => {
            // 这里暂时不用分析
            // config
            //     .plugin('webpack-bundle-analyzer')
            //     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin);

            // config
            //   .plugin('ScriptExtHtmlWebpackPlugin')
            //   .after('html')
            //   .use('script-ext-html-webpack-plugin', [{
            //   // `runtime` must same as runtimeChunk name. default is `runtime`
            //     inline: /runtime\..*\.js$/
            //   }])
            //   .end()
            config.optimization.splitChunks({
                chunks: 'all',
                minChunks: 1,
                cacheGroups: {
                    libs: {
                        name: 'chunk-libs',
                        test: /[\\/]node_modules[\\/]/,
                        priority: 20,
                        chunks: 'initial' // only package third parties that are initially dependent
                    },
                    echarts: {
                        name: 'chunk-echarts',
                        test: /[\\/]node_modules[\\/]echarts[\\/]/,
                        priority: 50,
                        enforce: true
                    },
                    xlsx: {
                        name: 'chunk-xlsx',
                        test: /[\\/]node_modules[\\/]xlsx[\\/]/,
                        priority: 40,
                        enforce: true
                    },
                    elementUI: {
                        name: 'chunk-elementUI', // split elementUI into a single package
                        test: /[\\/]node_modules[\\/]element-ui[\\/]/, // in order to adapt to cnpm
                        priority: 35,
                        enforce: true
                    },
                    tuieditor: {
                        name: 'chunk-tuieditor', // split elementUI into a single package
                        test: /[\\/]node_modules[\\/]tui-editor[\\/]/, // in order to adapt to cnpm
                        priority: 30,
                        enforce: true
                    },
                    modules: {
                        name: (module, chunks, cacheGroupKey) => {
                            const fileName = module.context.match(/[\\/]modules[\\/](.*?)([\\/]|$)/)[1];
                            // return 'modules/' + fileName + '/' + fileName;
                            return fileName;
                        },
                        priority: 5,
                        test: (module) => {
                            return /[\\/]modules[\\/]/.test(module.context);
                        },
                        chunks: 'all',
                        minSize: 0,
                        minChunks: 1,
                        enforce: true,
                        reuseExistingChunk: true
                    }
                }
            });
            // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
            config.optimization.runtimeChunk('single');
        });
    }
};
