<template>
    <div class="function-menu">
        <div v-for="item in functionMenu" :key="item.group" class="pattern">
            <div class="letter">
                {{ item.group }}
            </div>
            <div
                v-for="subitem in item.list"
                :key="subitem.permissionId"
                class="item"
                @click="gotoUrl(subitem.permissionUrl)"
            >
                <div>{{ subitem.permissionName }}</div>
            </div>
        </div>
    </div>
</template>
<script>
import Cookies from 'js-cookie';

export default {
    name: 'FunctionMenu',
    data() {
        return {
            functionMenu: []
        };
    },
    mounted() {
        this.getFunctionMenu();
    },
    methods: {
        /**
         * 获取功能导航数据
         */
        getFunctionMenu() {
            let langKey = '';
            if (Cookies.get('langKey')) {
                langKey = Cookies.get('langKey');
            }
            const menuData = {
                // 系统标识
                sysType: 'SYS-PC-WEB',
                langKey
            };
            // 获取菜单信息
            this.$service.frame
                .getFunctionMenu(menuData)
                .then((response) => {
                    if (response.code === '00') {
                        const menus = response.result;
                        for (const item in menus) {
                            if (item !== 'other') {
                                this.functionMenu.push({
                                    group: item,
                                    list: menus[item]
                                });
                            }
                        }
                    } else {
                        this.$message({
                            message: response.message,
                            type: 'error'
                        });
                    }
                })
                .catch((err) => {
                    console.error(err);
                });
        },
        // 根据url跳转到对应的地址
        gotoUrl(url) {
            //  如果是vue项目的，直接跳转路由，否则打开这个url
            if (url.indexOf('/warehouse') > -1) {
                const i = url.lastIndexOf('#');
                const path = url.slice(i + 1);
                this.$router.push({ path });
            } else {
                window.open(url, '_self');
            }
        }
    }
};
</script>
<style scoped lang="scss">
.function-menu {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    padding: 15px;
    padding-left: 0px;
    .pattern {
        width: 16.66666666%;
        margin-bottom: 15px;
        box-sizing: border-box;
        padding-left: 15px;
    }
    .item {
        width: 100%;
        div {
            width: 100%;
            line-height: 35px;
            padding-left: 10px;
            background: #f7f8fa;
            &:hover {
                cursor: pointer;
                background: #00c1de;
                color: #fff;
                font-size: 14px;
            }
        }
    }
}
</style>
