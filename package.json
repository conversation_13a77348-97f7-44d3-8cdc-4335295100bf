{"name": "wtf-frame", "version": "0.0.1", "private": false, "homepage": "", "scripts": {"analyzer": "use_analyzer=true npm run build", "dev": "vue-cli-service serve --host 127.0.0.1 --port 8000", "build": "vue-cli-service build", "build:module": "vue-cli-service build --target lib modules/icons/module_index.js", "build:test": "vue-cli-service build --mode test --target lib src/module_index.js", "new": "plop", "new:module": "plop module", "new:view": "plop view", "prepare": "husky install"}, "main": "dist/frame.umd.js", "license": "MIT", "repository": {"type": "git", "url": "http://*************/bbpf-platform-web/si-wtf-frame"}, "dependencies": {"@packy-tang/vue-tinymce": "^1.1.2", "@vue/cli-plugin-babel": "^4.4.4", "clipboard": "^2.0.11", "file-saver": "^2.0.5", "js-base64": "^3.7.2", "moment": "^2.29.4", "tinymce": "^5.10.2", "vue-cookies": "^1.8.2", "vue-cropper": "^0.5.8", "vue-infinite-loading": "^2.4.5", "vuex": "3.1.0", "wtf-core-vue-ng": "^2.0.0", "xlsx": "^0.17.4"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/eslint-parser": "^7.21.3", "@commitlint/cli": "^8.0.0", "@commitlint/config-conventional": "^8.0.0", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-service": "4.4.4", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "eslint": "^6.7.2", "eslint-config-prettier": "^8.8.0", "eslint-config-snbc": "^1.0.0-beta.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-vue": "^6.2.2", "highlight.js": "^9.18.5", "html-webpack-plugin": "3.2.0", "husky": "^8.0.0", "lint-staged": "8.1.5", "plop": "^2.3.0", "postcss-preset-env": "^6.6.0", "prettier": "^2.8.8", "prettier-config-snbc": "^1.0.0-beta.2", "runjs": "4.3.2", "sass": "1.32.12", "sass-loader": "^8.0.2", "script-loader": "^0.7.2", "svg-sprite-loader": "4.1.3", "vue-eslint-parser": "^9.1.1", "vue-template-compiler": "2.6.10", "webpack-bundle-analyzer": "^4.4.0"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "element-ui", "boilerplate", "admin-template", "management-system", "wtf-core-vue-ng"]}