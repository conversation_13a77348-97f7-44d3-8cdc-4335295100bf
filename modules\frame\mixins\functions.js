import Cookies from 'js-cookie';
// 获取BBPF1.5 菜单列表
export default {
    data() {
        return {};
    },
    computed: {},
    mounted() {
        // 通过tag切换打开后，将store的记录移除，该情况下页面参数可能特殊处理
        this.$store.commit('tagsView/TOGGLE_VIEW', null);
    },
    methods: {
        /**
         * 功能权限检查
         *
         * @param {String} permissionCode 权限编码
         * @returns {Boolean} 校验结果
         */
        checkPermission(permissionCode) {
            return this.$store.state.permission.btnDatas.includes(permissionCode);
        },
        /**
         * 遍历菜单数据，调整成跟BBPF2.0格式一样的
         *
         * @param {Array} menu 菜单数据
         * @returns {Array} menu
         */
        recursionMenu(menu) {
            const res = [];
            menu.forEach((item) => {
                item.children = item.nodes;
                item.meta = {};
                // 如果存在icon，则直接设置，否则设置为默认的icon
                if (!item.icon) {
                    item.icon = 'fa fa-square';
                }
                item.meta.icon = item.icon;
                item.meta.title = item.text;
                item.permissionIcon = item.icon;
                if (item.url && item.url.indexOf('https') > -1) {
                    if (process.env.NODE_ENV === 'development') {
                        item.path = item.url.replace(
                            'https://devbase.xinbeiyang.info/warehouse',
                            'http://local.xinbeiyang.info/warehouse'
                        );
                    } else {
                        item.path = item.url;
                    }
                } else {
                    item.path = item.id + item.code + item.text;
                }

                item.useLayout = true;
                if (item.children && item.children.length > 0) {
                    this.recursionMenu(item.children);
                } else {
                    item.children = [];
                }
                res.push(item);
            });
            return res;
        },
        /**
         * 获取左侧菜单数据
         */
        getPermissionList() {
            let langKey = '';
            if (Cookies.get('langKey')) {
                langKey = Cookies.get('langKey');
            }
            const menuData = {
                sysType: 'SYS-PC-WEB',
                langKey,
                permissionType: 'MENU'
            };
            // 获取菜单信息
            this.$service.frame
                .getMenuList(menuData)
                .then((response) => {
                    if (response.code === '00') {
                        //  遍历菜单数据，调整成跟BBPF2.0格式一样的
                        const menuList = this.recursionMenu(response.result);
                        this.$store.dispatch('permission/generateRoutes', menuList);
                        // 存储当前是否已经存在菜单
                        this.$store.dispatch('frame/saveMenuState', true);
                    } else {
                        this.$message({
                            message: response.message,
                            type: 'error'
                        });
                    }
                })
                .catch((err) => {
                    console.error(err);
                });
        },
        /**
         * 获取按钮数据权限
         */
        getBtnPermissionList() {
            let langKey = '';
            if (Cookies.get('langKey')) {
                langKey = Cookies.get('langKey');
            }
            const menuData = {
                sysType: 'SYS-PC-WEB',
                langKey,
                permissionType: 'FUNCTION'
            };
            // 获取菜单信息
            this.$service.frame
                .getMenuList(menuData)
                .then((response) => {
                    if (response.code === '00') {
                        const btnDatas = response.result.map((item) => item.code);
                        this.$store.dispatch('permission/btnPermissionData', btnDatas);
                    } else {
                        this.$message({
                            message: response.message,
                            type: 'error'
                        });
                    }
                })
                .catch((err) => {
                    console.error(err);
                });
        },
        /**
         * 如果当前没有菜单，则去调用菜单接口
         */
        isHasMenu() {
            // 如果token存在并且菜单已经存在，则不需要再去调用菜单接口，否则获取菜单
            const flag = this.$store.state.frame.hasMenu;
            if (!flag) {
                const { token } = this.$store.getters;
                if (token) {
                    this.$store.commit('user/SET_HADLOGIN', true);
                    this.getPermissionList();
                    this.getBtnPermissionList();
                }
            }
        },
        // 页面初始化监听头像下拉菜单的事件，便于之后的处理
        initNativeDrop() {
            // 监听修改密码
            if (!this.$eventBus.hadEvent('changePWDEvent')) {
                this.$eventBus.on('changePWDEvent', (_this) => {
                    const evnFlag = this.$tools.getEnvFlag();
                    const changePwdPageUrl = `https://${evnFlag}adm.xinbeiyang.info/#/login/edit_password`;
                    window.location.href = changePwdPageUrl;
                });
            }
            // 监听退出登录
            if (!this.$eventBus.hadEvent('logOutEvent')) {
                this.$eventBus.on('logOutEvent', (e) => {
                    this.$store.dispatch('user/logout');
                    const evnFlag = this.$tools.getEnvFlag();
                    const loginPageUrl = `https://${evnFlag}adm.xinbeiyang.info/#/login/index`;
                    window.location.href = loginPageUrl;
                });
            }
        }
    }
};
