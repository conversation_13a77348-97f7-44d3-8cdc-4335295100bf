<template>
    <div>
        <div v-show="show" class="message" @click="gotoMessage">
            <el-badge :value="unreadCount > 0 ? unreadCount : ''">
                <i class="el-icon-bell bell" />
            </el-badge>
        </div>
    </div>
</template>
<script>
import getUnreadCount from '../mixins/getUnreadCount';

export default {
    name: 'MessageManagement',
    mixins: [getUnreadCount],
    data() {
        return {
            // 是否展示铃铛
            show: true
        };
    },
    created() {
        this.getUnreadCount();
    },
    methods: {
        gotoMessage() {
            const evnFlag = this.$tools.getEnvFlag();
            const newsPageUrl = `https://${evnFlag}adm.xinbeiyang.info/#/app/news/receive_news`;
            window.open(newsPageUrl, '_blank');
        }
    }
};
</script>
<style lang="scss">
.el-message-box__wrapper {
    .el-message-box {
        .el-message-box__header {
            .el-message-box__title {
                font-size: 14px;
                font-weight: 700;
                color: #000000;
            }
        }
    }
}
</style>
<style scoped lang="scss">
.message {
    position: relative;
    margin-right: 20px;
    .bell {
        color: #fff;
    }
}
</style>
