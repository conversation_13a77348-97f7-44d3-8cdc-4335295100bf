// 网关列表 包含该网关下的服务列表。使用时根据网关+服务来使用
const getWayList = [
    {
        // key值，使用时key用这个
        getWayKey: 'systemApi',
        // 具体的值
        getWayValue: 'api',
        host: 'adm.xinbeiyang.info',
        services: {
            system: 'system'
        }
    },
    {
        getWayKey: 'workOrderApi',
        getWayValue: 'workorderapi',
        host: 'base.xinbeiyang.info',
        services: {
            base: ''
        }
    },
    {
        getWayKey: 'orderApi',
        getWayValue: 'orderapi',
        host: 'base.xinbeiyang.info',
        services: {
            base: ''
        }
    },
    {
        getWayKey: 'warehouseApi',
        getWayValue: 'api',
        host: 'adm.xinbeiyang.info',
        services: {
            base: 'warehouseapi'
        }
    },
    {
        getWayKey: 'assetApi',
        getWayValue: 'api',
        host: 'adm.xinbeiyang.info',
        services: {
            base: 'assetapi'
        }
    },
    {
        getWayKey: 'baseapi',
        getWayValue: 'api',
        host: 'base.xinbeiyang.info',
        services: {
            base: 'baseapi'
        }
    }
];
/**
 * 获取当前环境所在的基本url地址
 * @returns {string} 环境对应的基础URL地址
 */
const getEnvUrl = () => {
    const { hostname } = window.location;

    // 根据不同环境（本地/测试/生产），使用不同地址
    if (hostname === '127.0.0.1' || hostname === 'localhost' || hostname === 'http://**************') {
        // 测试环境
        return 'http://**************:7788/';
    }
    // 生产环境
    return 'http://************:7788/';
};

// 服务根url对象，包含：根域名+网关+服务
let basePath = null;

// 初始化服务路径，规则是取得当前url路径，解析出host，然后拼接网关，并存储到对象里。获取时：xxx.网关名称.服务名称
const initBaseUrl = (basePaths) => {
    const envBaseUrl = getEnvUrl();
    const basePathsObj = {};

    basePaths.map((path) => {
        basePathsObj[path.getWayKey] = {};
        for (const serviceName in path.services) {
            if (Object.hasOwnProperty.call(path.services, serviceName)) {
                // 根据环境构建不同的URL
                // 本地开发和测试环境：使用固定的IP地址
                const servicePath = path.services[serviceName] ? `/${path.services[serviceName]}` : '';
                basePathsObj[path.getWayKey][serviceName] = `${envBaseUrl}${path.getWayValue}${servicePath}`;
            }
        }
        return path;
    });
    basePath = basePathsObj;

    return basePath;
};

// 构造函数，参数可以传getWay数组进来，会追加到变量getWayList里
const basePathInit = (list) => {
    if (list && Array.isArray(list) && list.length > 0) {
        for (let index = 0; index < list.length; index++) {
            const element = list[index];
            if (element) {
                getWayList.push(element);
            }
        }
    }
    return initBaseUrl(getWayList);
};

export { basePathInit, getEnvUrl };
